"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Message } from "primereact/message";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { getOrgDataField } from "../../../utils/cookies";
import apiCall from "../../../utils/apiCallService";

interface PaymentReceiptFormProps {
    invoiceId?: string;
    invoiceType?: "sales" | "purchase";
}

interface InvoiceDetails {
    invoiceNo: string;
    invoiceDate: string;
    partyName: string;
    totalAmount: number;
    amountPaid: number;
    pendingAmount: number;
}

// API Response interfaces
interface InvoiceRecord {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    partyname: string;
    invoicetotal: number;
    amountpaid: number;
}

interface InvoiceApiResponse {
    invrecord: InvoiceRecord;
    olkstatus: number;
}

interface PaymentUpdateResponse {
    success?: number;
    olkstatus?: number;
    message?: string;
}

//  PaymentReceiptForm handles both payment (purchase) and receipt (sales) entries for invoices.
// It pre-fills invoice details, shows pending amount, and allows entering payment/receipt amount.

const PaymentReceiptForm: React.FC<PaymentReceiptFormProps> = ({
    invoiceId,
    invoiceType,
}) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { OLK_PATH } = useEnvContext();

    // Prefer props, fallback to query params
    const id = invoiceId || searchParams.get("invoiceId") || "";
    const type =
        invoiceType ||
        (searchParams.get("invoiceType") as "sales" | "purchase") ||
        "sales";

    // Get organization code from cookies
    const orgcode = Number(getOrgDataField("orgcode"));

    // State management
    const [invoice, setInvoice] = useState<InvoiceDetails | null>(null);
    const [pendingAmount, setPendingAmount] = useState<string>("0.00");
    const [amountPaid, setAmountPaid] = useState<string>("0.00");
    const [error, setError] = useState<string | null>(null);
    const [paymentDate, setPaymentDate] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(false);
    const [saving, setSaving] = useState<boolean>(false);

    // Fetch invoice details from API
    useEffect(() => {
        const fetchInvoiceDetails = async () => {
            if (!id || !OLK_PATH) {
                setError("Missing invoice ID or API configuration");
                return;
            }

            setLoading(true);
            setError(null);

            try {
                const response = await apiCall<InvoiceApiResponse>(
                    "GET",
                    `${OLK_PATH}/invoice/findinvoice?invid=${id}`
                );
                console.log("Get - ", response);

                if (response.data?.invrecord) {
                    const record = response.data.invrecord;
                    const pending = record.invoicetotal - record.amountpaid;

                    const invoiceDetails: InvoiceDetails = {
                        invoiceNo: record.invoiceno,
                        invoiceDate: new Date(
                            record.invoicedate
                        ).toLocaleDateString("en-GB"),
                        partyName: record.partyname,
                        totalAmount: record.invoicetotal,
                        amountPaid: record.amountpaid,
                        pendingAmount: pending,
                    };

                    setInvoice(invoiceDetails);
                    setPendingAmount(pending.toFixed(2));
                    setAmountPaid(pending.toFixed(2)); // Default to pending amount
                } else {
                    setError("Invoice not found");
                }
            } catch (err) {
                console.error("Error fetching invoice details:", err);
                setError("Failed to fetch invoice details");
            } finally {
                setLoading(false);
            }
        };

        fetchInvoiceDetails();
    }, [id, OLK_PATH]);

    // Handle amount paid change
    const handleAmountPaidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAmountPaid(e.target.value);
    };

    // Handle Save - call the payment update API
    const handleSave = async () => {
        // Validation
        if (
            !amountPaid ||
            isNaN(Number(amountPaid)) ||
            Number(amountPaid) <= 0
        ) {
            setError("Please enter a valid amount.");
            return;
        }

        if (!paymentDate) {
            setError("Please select a date for the payment/receipt.");
            return;
        }

        const amountValue = Number(amountPaid);
        const pendingValue = Number(pendingAmount);

        if (amountValue > pendingValue) {
            setError(
                `Amount paid cannot exceed pending amount of ₹${pendingAmount}`
            );
            return;
        }

        if (!orgcode || !id) {
            setError("Missing required information");
            return;
        }

        setSaving(true);
        setError(null);

        try {
            const payload = {
                orgcode: orgcode,
                invid: Number(id),
                amt: amountValue,
                paymentdate: paymentDate,
            };

            const response = await apiCall<PaymentUpdateResponse>(
                "PUT",
                `${OLK_PATH}/invoice/updatepayment`,
                payload
            );

            if (response.data?.success === 1 || response.status === 200) {
                // Success - redirect back to invoice list
                router.push("/dashboard/invoice/sales-and-purchase/list");
            } else {
                setError(response.data?.message || "Failed to update payment");
            }
        } catch (err) {
            console.error("Error updating payment:", err);
            setError("Failed to update payment. Please try again.");
        } finally {
            setSaving(false);
        }
    };

    // Handle Cancel
    const handleCancel = () => {
        router.back();
    };

    if (loading) {
        return (
            <div className="p-4 text-center text-gray-500">
                Loading invoice details...
            </div>
        );
    }

    if (!invoice) {
        return (
            <div className="p-4 text-center text-red-500">
                {error || "Invoice not found"}
            </div>
        );
    }

    return (
        <div className="flex justify-center items-start pt-2 px-4">
            <div className="max-w-xl bg-white rounded-lg shadow-md p-4 m-auto">
                <h2 className="text-xl font-semibold mb-4 text-center">
                    {type === "sales" ? "Receipt" : "Payment"} Entry
                </h2>
                {error && (
                    <Message severity="error" text={error} className="mb-3" />
                )}
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Invoice No
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.invoiceNo}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">Date</label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.invoiceDate}
                        </div>
                    </div>
                </div>
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            {type === "sales" ? "Customer" : "Supplier"} Details
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            {invoice.partyName}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Total Invoice Amount
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-gray-700 text-xl">
                            ₹
                            {invoice.totalAmount.toLocaleString("en-IN", {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                            })}
                        </div>
                    </div>
                </div>
                <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            Pending Amount
                        </label>
                        <div className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200 text-red-600 font-semibold text-xl">
                            ₹{pendingAmount}
                        </div>
                    </div>
                    <div className="flex-1">
                        <label className="block font-medium mb-1">
                            {type === "sales" ? "Receipt" : "Payment"} Date{" "}
                            <span className="text-red-500">*</span>
                        </label>
                        <InputText
                            type="date"
                            value={paymentDate}
                            onChange={(e) => setPaymentDate(e.target.value)}
                            className="w-full"
                            required
                        />
                    </div>
                </div>
                <div className="mb-3">
                    <label className="block font-medium mb-1">
                        Amount Paid
                    </label>
                    <InputText
                        value={amountPaid}
                        onChange={handleAmountPaidChange}
                        className="w-full text-xl"
                        type="number"
                        min="0"
                        max={pendingAmount}
                    />
                </div>
                <div className="flex gap-3 mt-4 justify-end">
                    <Button
                        label={saving ? "Saving..." : "Save"}
                        icon={saving ? "pi pi-spin pi-spinner" : "pi pi-check"}
                        onClick={handleSave}
                        disabled={saving}
                        className="p-button-success"
                    />
                    <Button
                        label="Cancel"
                        icon="pi pi-times"
                        onClick={handleCancel}
                        disabled={saving}
                        className="p-button-secondary"
                    />
                </div>
            </div>
        </div>
    );
};

export default PaymentReceiptForm;
